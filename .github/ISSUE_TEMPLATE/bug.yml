name: Bug Report
description: Create a bug report
labels: ["C-bug", "S-needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! Please provide as much detail as possible.

        If you believe you have found a vulnerability, please provide details [here](mailto:<EMAIL>) instead.
  - type: textarea
    id: what-happened
    attributes:
      label: Describe the bug
      description: |
        A clear and concise description of what the bug is.

        If the bug is in a crate you are using (i.e. you are not running the standard `reth` binary) please mention that as well.
    validations:
      required: true
  - type: textarea
    id: reproduction-steps
    attributes:
      label: Steps to reproduce
      description: Please provide any steps you think might be relevant to reproduce the bug.
      placeholder: |
        Steps to reproduce:

        1. Start '...'
        2. Then '...'
        3. Check '...'
        4. See error
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Node logs
      description: |
        If applicable, please provide the node logs leading up to the bug.

        **Please also provide debug logs.** By default, these can be found in:

        - `~/.cache/reth/logs` on Linux
        - `~/Library/Caches/reth/logs` on macOS
        - `%localAppData%/reth/logs` on Windows
      render: text
    validations:
      required: false
  - type: dropdown
    id: platform
    attributes:
      label: Platform(s)
      description: What platform(s) did this occur on?
      multiple: true
      options:
        - Linux (x86)
        - Linux (ARM)
        - Mac (Intel)
        - Mac (Apple Silicon)
        - Windows (x86)
        - Windows (ARM)
  - type: dropdown
    id: container_type
    attributes:
      label: Container Type
      description: Were you running it in a container?
      multiple: true
      options:
        - Not running in a container
        - Docker
        - Kubernetes
        - LXC/LXD
        - Other
    validations:
      required: true
  - type: textarea
    id: client-version
    attributes:
      label: What version/commit are you on?
      description: This can be obtained with `reth --version`
    validations:
      required: true
  - type: textarea
    id: database-version
    attributes:
      label: What database version are you on?
      description: This can be obtained with `reth db version`
    validations:
      required: true
  - type: textarea
    id: network
    attributes:
      label: Which chain / network are you on?
      description: This is the argument you pass to `reth --chain`. If you are using `--dev`, type in 'dev' here. If you are not running with `--chain` or `--dev` then it is mainnet.
    validations:
      required: true
  - type: dropdown
    id: node-type
    attributes:
      label: What type of node are you running?
      options:
        - Archive (default)
        - Full via --full flag
        - Pruned with custom reth.toml config
    validations:
      required: true
  - type: textarea
    id: prune-config
    attributes:
      label: What prune config do you use, if any?
      description: The `[prune]` section in `reth.toml` file
    validations:
      required: false
  - type: input
    attributes:
      label: If you've built Reth from source, provide the full command you used
    validations:
      required: false
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/paradigmxyz/reth/blob/main/CONTRIBUTING.md#code-of-conduct)
      options:
        - label: I agree to follow the Code of Conduct
          required: true
