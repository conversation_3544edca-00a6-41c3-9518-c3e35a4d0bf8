name: Prepare Reth Image

on:
  workflow_call:
    inputs:
      image_tag:
        required: true
        type: string
        description: "Docker image tag to use"
      binary_name:
        required: false
        type: string
        default: "reth"
        description: "Binary name to build (reth or op-reth)"
      cargo_features:
        required: false
        type: string
        default: "asm-keccak"
        description: "Cargo features to enable"
      cargo_package:
        required: false
        type: string
        description: "Optional cargo package path"

jobs:
  prepare-reth:
    if: github.repository == 'paradigmxyz/reth'
    timeout-minutes: 45
    runs-on:
      group: Reth
    steps:
      - uses: actions/checkout@v4
      - run: mkdir artifacts

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and export reth image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: .github/assets/hive/Dockerfile
          tags: ${{ inputs.image_tag }}
          outputs: type=docker,dest=./artifacts/reth_image.tar
          build-args: |
            CARGO_BIN=${{ inputs.binary_name }}
            MANIFEST_PATH=${{ inputs.cargo_package }}
            FEATURES=${{ inputs.cargo_features }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Upload reth image
        id: upload
        uses: actions/upload-artifact@v4
        with:
          name: artifacts
          path: ./artifacts
