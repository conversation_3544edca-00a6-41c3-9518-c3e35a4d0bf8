# Runs all `stage run` commands.

name: stage-test

on:
  pull_request:
  merge_group:
  push:
    branches: [main]

env:
  CARGO_TERM_COLOR: always
  FROM_BLOCK: 0
  TO_BLOCK: 50000

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  stage:
    name: stage-run-test
    # Only run stage commands test in merge groups
    if: github.event_name == 'merge_group'
    runs-on:
      group: Reth
    env:
      RUST_LOG: info,sync=error
      RUST_BACKTRACE: 1
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
      - name: Build reth
        run: |
          cargo install --features asm-keccak,jemalloc --path bin/reth
      - name: Run headers stage
        run: |
          reth stage run headers --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run bodies stage
        run: |
          reth stage run bodies --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run senders stage
        run: |
          reth stage run senders --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run execution stage
        run: |
          reth stage run execution --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run account-hashing stage
        run: |
          reth stage run account-hashing --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run storage hashing stage
        run: |
          reth stage run storage-hashing --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run hashing stage
        run: |
          reth stage run hashing --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run merkle stage
        run: |
          reth stage run merkle --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run transaction lookup stage
        run: |
          reth stage run tx-lookup --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run account history stage
        run: |
          reth stage run account-history --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
      - name: Run storage history stage
        run: |
          reth stage run storage-history --from ${{ env.FROM_BLOCK }} --to ${{ env.TO_BLOCK }} --commit --checkpoints
