# Runs unit tests.

name: unit

on:
  pull_request:
  merge_group:
  push:
    branches: [main]

env:
  CARGO_TERM_COLOR: always
  SEED: rustethereumethereumrust

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    name: test / ${{ matrix.type }} (${{ matrix.partition }}/${{ matrix.total_partitions }})
    runs-on:
      group: Reth
    env:
      RUST_BACKTRACE: 1
    strategy:
      matrix:
        include:
          - type: ethereum
            args: --features "asm-keccak ethereum" --locked
            partition: 1
            total_partitions: 2
          - type: ethereum
            args: --features "asm-keccak ethereum" --locked
            partition: 2
            total_partitions: 2
          - type: optimism
            args: --features "asm-keccak" --locked --exclude reth --exclude reth-bench --exclude "example-*" --exclude "reth-ethereum-*" --exclude "*-ethereum"
            partition: 1
            total_partitions: 2
          - type: optimism
            args: --features "asm-keccak" --locked --exclude reth --exclude reth-bench --exclude "example-*" --exclude "reth-ethereum-*" --exclude "*-ethereum"
            partition: 2
            total_partitions: 2
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
      - uses: taiki-e/install-action@nextest
      - if: "${{ matrix.type == 'book' }}"
        uses: arduino/setup-protoc@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Run tests
        run: |
          cargo nextest run \
            ${{ matrix.args }} --workspace \
            --exclude ef-tests --no-tests=warn \
            --partition hash:${{ matrix.partition }}/2 \
            -E "!kind(test) and not binary(e2e_testsuite)"

  state:
    name: Ethereum state tests
    runs-on:
      group: Reth
    env:
      RUST_LOG: info,sync=error
      RUST_BACKTRACE: 1
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - name: Checkout ethereum/tests
        uses: actions/checkout@v4
        with:
          repository: ethereum/tests
          ref: 81862e4848585a438d64f911a19b3825f0f4cd95
          path: testing/ef-tests/ethereum-tests
          submodules: recursive
          fetch-depth: 1
      - uses: rui314/setup-mold@v1
      - uses: dtolnay/rust-toolchain@stable
      - uses: taiki-e/install-action@nextest
      - uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
      - run: cargo nextest run --release -p ef-tests --features "asm-keccak ef-tests"

  doc:
    name: doc tests
    runs-on:
      group: Reth
    env:
      RUST_BACKTRACE: 1
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
      - name: Run doctests
        run: cargo test --doc --workspace --all-features

  unit-success:
    name: unit success
    runs-on: ubuntu-latest
    if: always()
    needs: [test, state, doc]
    timeout-minutes: 30
    steps:
      - name: Decide whether the needed jobs succeeded or failed
        uses: re-actors/alls-green@release/v1
        with:
          jobs: ${{ toJSON(needs) }}
