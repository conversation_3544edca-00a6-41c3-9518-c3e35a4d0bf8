diff --git a/internal/libdocker/builder.go b/internal/libdocker/builder.go
index e4bf99b6..2023f7e2 100644
--- a/internal/libdocker/builder.go
+++ b/internal/libdocker/builder.go
@@ -8,7 +8,6 @@ import (
 	"io"
 	"io/fs"
 	"log/slog"
-	"os"
 	"path/filepath"
 	"slices"
 	"strings"
@@ -49,25 +48,8 @@ func (b *Builder) BuildClientImage(ctx context.Context, client libhive.ClientDes
 
 // BuildSimulatorImage builds a docker image of a simulator.
 func (b *Builder) BuildSimulatorImage(ctx context.Context, name string, buildArgs map[string]string) (string, error) {
-	dir := b.config.Inventory.SimulatorDirectory(name)
-	buildContextPath := dir
-	buildDockerfile := "Dockerfile"
-
-	// build context dir of simulator can be overridden with "hive_context.txt" file containing the desired build path
-	if contextPathBytes, err := os.ReadFile(filepath.Join(filepath.FromSlash(dir), "hive_context.txt")); err == nil {
-		buildContextPath = filepath.Join(dir, strings.TrimSpace(string(contextPathBytes)))
-		if strings.HasPrefix(buildContextPath, "../") {
-			return "", fmt.Errorf("cannot access build directory outside of Hive root: %q", buildContextPath)
-		}
-		if p, err := filepath.Rel(buildContextPath, filepath.Join(filepath.FromSlash(dir), "Dockerfile")); err != nil {
-			return "", fmt.Errorf("failed to derive relative simulator Dockerfile path: %v", err)
-		} else {
-			buildDockerfile = p
-		}
-	}
 	tag := fmt.Sprintf("hive/simulators/%s:latest", name)
-	err := b.buildImage(ctx, buildContextPath, buildDockerfile, tag, buildArgs)
-	return tag, err
+	return tag, nil
 }
 
 // BuildImage creates a container by archiving the given file system,
diff --git a/internal/libdocker/proxy.go b/internal/libdocker/proxy.go
index d3a14ae6..8779671e 100644
--- a/internal/libdocker/proxy.go
+++ b/internal/libdocker/proxy.go
@@ -16,7 +16,7 @@ const hiveproxyTag = "hive/hiveproxy"
 
 // Build builds the hiveproxy image.
 func (cb *ContainerBackend) Build(ctx context.Context, b libhive.Builder) error {
-	return b.BuildImage(ctx, hiveproxyTag, hiveproxy.Source)
+	return nil
 }
 
 // ServeAPI starts the API server.
