[package]
name = "reth-evm-ethereum"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# Reth
reth-execution-types.workspace = true
reth-chainspec.workspace = true
reth-ethereum-forks.workspace = true
reth-primitives-traits.workspace = true
reth-ethereum-primitives.workspace = true
revm.workspace = true
reth-evm.workspace = true

# Alloy
alloy-primitives.workspace = true
alloy-eips.workspace = true
alloy-evm.workspace = true
alloy-consensus.workspace = true

# Misc
parking_lot = { workspace = true, optional = true }
derive_more = { workspace = true, optional = true }

[dev-dependencies]
reth-testing-utils.workspace = true
reth-evm = { workspace = true, features = ["test-utils"] }
secp256k1.workspace = true
alloy-genesis.workspace = true

[features]
default = ["std"]
std = [
    "alloy-consensus/std",
    "alloy-eips/std",
    "alloy-genesis/std",
    "alloy-primitives/std",
    "secp256k1/std",
    "reth-ethereum-forks/std",
    "reth-chainspec/std",
    "alloy-evm/std",
    "reth-execution-types/std",
    "reth-evm/std",
    "reth-primitives-traits/std",
    "revm/std",
    "reth-ethereum-primitives/std",
    "derive_more?/std",
]
test-utils = [
    "dep:parking_lot",
    "dep:derive_more",
    "reth-chainspec/test-utils",
    "reth-ethereum-primitives/test-utils",
    "reth-evm/test-utils",
    "reth-primitives-traits/test-utils",
]
