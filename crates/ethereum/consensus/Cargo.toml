[package]
name = "reth-ethereum-consensus"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-execution-types.workspace = true
reth-chainspec.workspace = true
reth-consensus-common.workspace = true
reth-primitives-traits.workspace = true
reth-consensus.workspace = true

# alloy
alloy-eips.workspace = true
alloy-primitives.workspace = true
alloy-consensus.workspace = true

tracing.workspace = true

[features]
default = ["std"]
std = [
    "alloy-consensus/std",
    "alloy-eips/std",
    "alloy-primitives/std",
    "reth-chainspec/std",
    "reth-consensus/std",
    "reth-consensus-common/std",
    "reth-ethereum-primitives/std",
    "reth-execution-types/std",
    "reth-primitives-traits/std",
    "tracing/std",
]

[dev-dependencies]
reth-ethereum-primitives.workspace = true
alloy-primitives = { workspace = true, features = ["getrandom"] }
