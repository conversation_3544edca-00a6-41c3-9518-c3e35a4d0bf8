[package]
name = "reth-ethereum-primitives"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Ethereum primitive types"

[lints]
workspace = true

[dependencies]
# reth
reth-codecs = { workspace = true, optional = true }
reth-primitives-traits.workspace = true
reth-zstd-compressors = { workspace = true, optional = true }

# ethereum
alloy-eips = { workspace = true, features = ["k256"] }
alloy-primitives.workspace = true
alloy-consensus = { workspace = true, features = ["serde"] }
alloy-rlp.workspace = true

# misc
arbitrary = { workspace = true, optional = true, features = ["derive"] }
modular-bitfield = { workspace = true, optional = true }
serde = { workspace = true, optional = true }
serde_with = { workspace = true, optional = true }

[dev-dependencies]
derive_more.workspace = true
arbitrary.workspace = true
bincode.workspace = true
proptest.workspace = true
proptest-arbitrary-interop.workspace = true
rand_08.workspace = true
rand.workspace = true
reth-codecs = { workspace = true, features = ["test-utils"] }
reth-zstd-compressors.workspace = true
secp256k1 = { workspace = true, features = ["rand"] }
alloy-consensus = { workspace = true, features = ["serde", "arbitrary"] }

[features]
default = ["std"]
test-utils = [
    "reth-codecs?/test-utils",
    "reth-primitives-traits/test-utils",
]
std = [
    "alloy-consensus/std",
    "alloy-primitives/std",
    "alloy-rlp/std",
    "reth-primitives-traits/std",
    "reth-zstd-compressors?/std",
    "serde?/std",
    "alloy-eips/std",
    "derive_more/std",
    "serde_with?/std",
    "secp256k1/std",
]
reth-codec = [
    "std",
    "dep:reth-codecs",
    "dep:modular-bitfield",
    "dep:reth-zstd-compressors",
]
arbitrary = [
    "dep:arbitrary",
    "alloy-consensus/arbitrary",
    "alloy-consensus/k256",
    "alloy-primitives/arbitrary",
    "reth-codecs?/arbitrary",
    "reth-primitives-traits/arbitrary",
    "alloy-eips/arbitrary",
]
serde-bincode-compat = [
    "dep:serde_with",
    "alloy-consensus/serde-bincode-compat",
    "alloy-eips/serde-bincode-compat",
    "reth-primitives-traits/serde-bincode-compat",
]
serde = [
    "dep:serde",
    "alloy-consensus/serde",
    "alloy-eips/serde",
    "alloy-primitives/serde",
    "reth-codecs?/serde",
    "reth-primitives-traits/serde",
    "rand/serde",
    "rand_08/serde",
    "secp256k1/serde",
]
