// Generated by fetch_superchain_config.sh
use crate::create_superchain_specs;

create_superchain_specs!(
    ("arena-z", "mainnet"),
    ("arena-z-testnet", "sepolia"),
    ("automata", "mainnet"),
    ("base-devnet-0", "sepolia-dev-0"),
    ("bob", "mainnet"),
    ("boba", "sepolia"),
    ("creator-chain-testnet", "sepolia"),
    ("cyber", "mainnet"),
    ("cyber", "sepolia"),
    ("ethernity", "mainnet"),
    ("ethernity", "sepolia"),
    ("funki", "mainnet"),
    ("funki", "sepolia"),
    ("hashkeychain", "mainnet"),
    ("ink", "mainnet"),
    ("ink", "sepolia"),
    ("lisk", "mainnet"),
    ("lisk", "sepolia"),
    ("lyra", "mainnet"),
    ("metal", "mainnet"),
    ("metal", "sepolia"),
    ("mint", "mainnet"),
    ("mode", "mainnet"),
    ("mode", "sepolia"),
    ("oplabs-devnet-0", "sepolia-dev-0"),
    ("orderly", "mainnet"),
    ("pivotal", "sepolia"),
    ("polynomial", "mainnet"),
    ("race", "mainnet"),
    ("race", "sepolia"),
    ("redstone", "mainnet"),
    ("settlus-mainnet", "mainnet"),
    ("settlus-sepolia", "sepolia"),
    ("shape", "mainnet"),
    ("shape", "sepolia"),
    ("snax", "mainnet"),
    ("soneium", "mainnet"),
    ("soneium-minato", "sepolia"),
    ("sseed", "mainnet"),
    ("swan", "mainnet"),
    ("swell", "mainnet"),
    ("tbn", "mainnet"),
    ("tbn", "sepolia"),
    ("unichain", "mainnet"),
    ("unichain", "sepolia"),
    ("worldchain", "mainnet"),
    ("worldchain", "sepolia"),
    ("xterio-eth", "mainnet"),
    ("zora", "mainnet"),
    ("zora", "sepolia"),
);
