[package]
name = "op-reth"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
reth-cli-util.workspace = true
reth-optimism-cli.workspace = true
reth-optimism-rpc.workspace = true
reth-optimism-node = { workspace = true, features = ["js-tracer"] }
reth-optimism-chainspec.workspace = true
reth-optimism-consensus.workspace = true
reth-optimism-evm.workspace = true
reth-optimism-payload-builder.workspace = true
reth-optimism-primitives.workspace = true
reth-optimism-forks.workspace = true

clap = { workspace = true, features = ["derive", "env"] }
tracing.workspace = true

[lints]
workspace = true

[features]
default = ["jemalloc", "reth-optimism-evm/portable"]

jemalloc = ["reth-cli-util/jemalloc", "reth-optimism-cli/jemalloc"]
jemalloc-prof = ["reth-cli-util/jemalloc-prof"]
tracy-allocator = ["reth-cli-util/tracy-allocator"]

asm-keccak = ["reth-optimism-cli/asm-keccak", "reth-optimism-node/asm-keccak"]

dev = [
    "reth-optimism-cli/dev",
    "reth-optimism-primitives/arbitrary",
]

min-error-logs = ["tracing/release_max_level_error"]
min-warn-logs = ["tracing/release_max_level_warn"]
min-info-logs = ["tracing/release_max_level_info"]
min-debug-logs = ["tracing/release_max_level_debug"]
min-trace-logs = ["tracing/release_max_level_trace"]

[[bin]]
name = "op-reth"
path = "src/main.rs"
