[package]
name = "reth-era-utils"
description = "Utilities to store and fetch history data with storage-api"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
# alloy
alloy-consensus.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true

# reth
reth-db-api.workspace = true
reth-era.workspace = true
reth-era-downloader.workspace = true
reth-etl.workspace = true
reth-ethereum-primitives.workspace = true
reth-fs-util.workspace = true
reth-provider.workspace = true
reth-stages-types.workspace = true
reth-storage-api.workspace = true
reth-primitives-traits.workspace = true

# async
tokio = { workspace = true, features = ["fs", "io-util", "macros", "rt-multi-thread"] }
futures-util.workspace = true

# errors
eyre.workspace = true
tracing.workspace = true

[dev-dependencies]
# reth
reth-provider.workspace = true
reth-provider.features = ["test-utils"]
reth-db-common.workspace = true

# async
tokio-util.workspace = true
futures.workspace = true
bytes.workspace = true

# http
reqwest.workspace = true

# file system
tempfile.workspace = true

[lints]
workspace = true
