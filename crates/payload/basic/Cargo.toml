[package]
name = "reth-basic-payload-builder"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "A basic payload builder for reth that uses the txpool API to build payloads."

[lints]
workspace = true

[dependencies]
# reth
reth-primitives-traits.workspace = true
reth-payload-builder.workspace = true
reth-payload-builder-primitives.workspace = true
reth-payload-primitives.workspace = true
reth-tasks.workspace = true
reth-revm.workspace = true
reth-storage-api.workspace = true
reth-chain-state.workspace = true

# ethereum
alloy-primitives.workspace = true
alloy-consensus.workspace = true
alloy-eips.workspace = true

# async
tokio = { workspace = true, features = ["sync", "time"] }
futures-core.workspace = true
futures-util.workspace = true

# metrics
reth-metrics.workspace = true
metrics.workspace = true

# misc
tracing.workspace = true
