[package]
name = "reth-payload-builder"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "reth payload builder"

[lints]
workspace = true

[dependencies]
# reth
reth-primitives-traits.workspace = true
reth-chain-state.workspace = true
reth-payload-builder-primitives.workspace = true
reth-payload-primitives.workspace = true
reth-ethereum-engine-primitives.workspace = true

# alloy
alloy-consensus.workspace = true
alloy-primitives = { workspace = true, optional = true }
alloy-rpc-types = { workspace = true, features = ["engine"] }

# async
tokio = { workspace = true, features = ["sync"] }
tokio-stream.workspace = true
futures-util.workspace = true

# metrics
reth-metrics.workspace = true
metrics.workspace = true

# misc
tracing.workspace = true

[dev-dependencies]
alloy-primitives.workspace = true

tokio = { workspace = true, features = ["sync", "rt"] }

[features]
test-utils = [
    "alloy-primitives",
    "reth-chain-state/test-utils",
    "reth-primitives-traits/test-utils",
    "tokio/rt",
]
