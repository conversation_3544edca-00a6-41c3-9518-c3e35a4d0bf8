[package]
name = "reth-payload-validator"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Payload validation support"

[lints]
workspace = true

[dependencies]
# reth
reth-primitives-traits.workspace = true

# alloy
alloy-rpc-types-engine.workspace = true
alloy-consensus.workspace = true

[features]
default = ["std"]
std = [
    "alloy-consensus/std",
    "alloy-rpc-types-engine/std",
    "reth-primitives-traits/std",
]
