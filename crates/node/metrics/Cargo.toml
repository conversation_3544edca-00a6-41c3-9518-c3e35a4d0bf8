[package]
name = "reth-node-metrics"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[dependencies]
reth-metrics.workspace = true
reth-tasks.workspace = true

metrics.workspace = true
metrics-exporter-prometheus.workspace = true
metrics-process.workspace = true
metrics-util.workspace = true

tokio.workspace = true

jsonrpsee-server.workspace = true
http.workspace = true
tower.workspace = true

tracing.workspace = true
eyre.workspace = true

[target.'cfg(unix)'.dependencies]
tikv-jemalloc-ctl = { workspace = true, optional = true, features = ["stats"] }

[target.'cfg(target_os = "linux")'.dependencies]
procfs = "0.17.0"

[dev-dependencies]
reqwest.workspace = true
socket2.workspace = true

[lints]
workspace = true

[features]
jemalloc = ["dep:tikv-jemalloc-ctl"]
