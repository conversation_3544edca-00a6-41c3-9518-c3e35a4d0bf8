[package]
name = "reth-node-core"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-chainspec.workspace = true
reth-consensus.workspace = true
reth-primitives-traits = { workspace = true, features = ["rayon"] }
reth-cli-util.workspace = true
reth-db = { workspace = true, features = ["mdbx"] }
reth-storage-errors.workspace = true
reth-storage-api = { workspace = true, features = ["std", "db-api"] }
reth-network = { workspace = true, features = ["serde"] }
reth-network-p2p.workspace = true
reth-rpc-eth-types.workspace = true
reth-rpc-server-types.workspace = true
reth-rpc-convert.workspace = true
reth-transaction-pool.workspace = true
reth-tracing.workspace = true
reth-config = { workspace = true, features = ["serde"] }
reth-discv4.workspace = true
reth-discv5.workspace = true
reth-net-nat.workspace = true
reth-network-peers.workspace = true
reth-prune-types.workspace = true
reth-stages-types.workspace = true
reth-ethereum-forks.workspace = true
reth-engine-local.workspace = true
reth-engine-primitives.workspace = true

# ethereum
alloy-primitives.workspace = true
alloy-rpc-types-engine = { workspace = true, features = ["std", "jwt"] }
alloy-consensus.workspace = true
alloy-eips.workspace = true

# misc
eyre.workspace = true
clap = { workspace = true, features = ["derive"] }
humantime.workspace = true
rand.workspace = true
derive_more.workspace = true
toml.workspace = true
serde.workspace = true
strum = { workspace = true, features = ["derive"] }
thiserror.workspace = true
url.workspace = true

# io
dirs-next.workspace = true
shellexpand.workspace = true

# tracing
tracing.workspace = true

# crypto
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery"] }

# async
futures.workspace = true

[dev-dependencies]
# test vectors generation
proptest.workspace = true
tokio.workspace = true

[features]
# Features for vergen to generate correct env vars
jemalloc = ["reth-cli-util/jemalloc"]
asm-keccak = ["alloy-primitives/asm-keccak"]

[build-dependencies]
vergen = { workspace = true, features = ["build", "cargo", "emit_and_set"] }
vergen-git2.workspace = true
