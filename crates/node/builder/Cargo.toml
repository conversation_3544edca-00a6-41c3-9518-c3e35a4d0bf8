[package]
name = "reth-node-builder"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
readme = "README.md"

[lints]
workspace = true

[dependencies]
## reth
reth-chain-state.workspace = true
reth-chainspec.workspace = true
reth-cli-util.workspace = true
reth-config.workspace = true
reth-consensus-debug-client.workspace = true
reth-consensus.workspace = true
reth-db = { workspace = true, features = ["mdbx"], optional = true }
reth-db-api.workspace = true
reth-db-common.workspace = true
reth-downloaders.workspace = true
reth-engine-local.workspace = true
reth-engine-primitives.workspace = true
reth-engine-service.workspace = true
reth-engine-tree.workspace = true
reth-engine-util.workspace = true
reth-evm.workspace = true
reth-exex.workspace = true
reth-fs-util.workspace = true
reth-invalid-block-hooks.workspace = true
reth-network-api.workspace = true
reth-network-p2p.workspace = true
reth-network.workspace = true
reth-node-api.workspace = true
reth-node-core.workspace = true
reth-node-events.workspace = true
reth-node-metrics.workspace = true
reth-payload-builder.workspace = true
reth-primitives-traits.workspace = true
reth-provider.workspace = true
reth-prune.workspace = true
reth-rpc.workspace = true
reth-rpc-api.workspace = true
reth-rpc-builder.workspace = true
reth-rpc-engine-api.workspace = true
reth-rpc-eth-types.workspace = true
reth-rpc-layer.workspace = true
reth-stages.workspace = true
reth-static-file.workspace = true
reth-tasks.workspace = true
reth-tokio-util.workspace = true
reth-tracing.workspace = true
reth-transaction-pool.workspace = true
reth-basic-payload-builder.workspace = true
reth-node-ethstats.workspace = true

## ethereum
alloy-consensus.workspace = true
alloy-primitives.workspace = true
alloy-provider.workspace = true
alloy-rpc-types = { workspace = true, features = ["engine"] }
alloy-eips = { workspace = true, features = ["kzg"] }
alloy-rpc-types-engine.workspace = true

## async
futures.workspace = true
tokio = { workspace = true, features = ["sync", "macros", "time", "rt-multi-thread"] }
tokio-stream.workspace = true

## crypto
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery"] }

## misc
aquamarine.workspace = true
eyre.workspace = true
jsonrpsee.workspace = true
fdlimit.workspace = true
rayon.workspace = true
serde_json.workspace = true

# tracing
tracing.workspace = true

[dev-dependencies]
tempfile.workspace = true
reth-ethereum-engine-primitives.workspace = true
reth-payload-builder = { workspace = true, features = ["test-utils"] }
reth-node-ethereum.workspace = true
reth-provider = { workspace = true, features = ["test-utils"] }
reth-evm-ethereum = { workspace = true, features = ["test-utils"] }

[features]
default = []
js-tracer = ["reth-rpc/js-tracer"]
test-utils = [
    "dep:reth-db",
    "reth-db/test-utils",
    "reth-chain-state/test-utils",
    "reth-chainspec/test-utils",
    "reth-consensus/test-utils",
    "reth-engine-tree/test-utils",
    "reth-evm/test-utils",
    "reth-downloaders/test-utils",
    "reth-network/test-utils",
    "reth-network-p2p/test-utils",
    "reth-payload-builder/test-utils",
    "reth-stages/test-utils",
    "reth-db-api/test-utils",
    "reth-provider/test-utils",
    "reth-transaction-pool/test-utils",
    "reth-evm-ethereum/test-utils",
    "reth-node-ethereum/test-utils",
    "reth-primitives-traits/test-utils",
]
op = [
    "reth-db?/op",
    "reth-db-api/op",
    "reth-engine-local/op",
    "reth-evm/op",
    "reth-primitives-traits/op",
]
