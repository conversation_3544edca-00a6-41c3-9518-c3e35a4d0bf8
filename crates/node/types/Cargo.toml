[package]
name = "reth-node-types"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-chainspec.workspace = true
reth-db-api.workspace = true
reth-engine-primitives.workspace = true
reth-primitives-traits.workspace = true
reth-trie-db.workspace = true
reth-payload-primitives.workspace = true

[features]
default = ["std"]
std = [
    "reth-primitives-traits/std",
    "reth-chainspec/std",
    "reth-engine-primitives/std",
]
