[package]
name = "reth-node-ethstats"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
reth-network-api.workspace = true
reth-transaction-pool.workspace = true
reth-primitives-traits.workspace = true
reth-storage-api.workspace = true
reth-chain-state.workspace = true

alloy-primitives.workspace = true
alloy-consensus.workspace = true

tokio.workspace = true
tokio-tungstenite = { workspace = true, features = ["rustls-tls-native-roots"] }
futures-util.workspace = true
tokio-stream.workspace = true

serde.workspace = true
serde_json.workspace = true

tracing.workspace = true
url.workspace = true
chrono.workspace = true
thiserror = { workspace = true, features = ["std"] }
