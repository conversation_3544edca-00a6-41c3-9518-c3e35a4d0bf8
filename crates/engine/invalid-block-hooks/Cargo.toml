[package]
name = "reth-invalid-block-hooks"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
revm-bytecode.workspace = true
reth-chainspec.workspace = true
revm-database.workspace = true
reth-engine-primitives.workspace = true
reth-evm.workspace = true
reth-primitives-traits.workspace = true
reth-provider.workspace = true
reth-revm = { workspace = true, features = ["serde"] }
reth-rpc-api = { workspace = true, features = ["client"] }
reth-tracing.workspace = true
reth-trie.workspace = true

# alloy
alloy-primitives.workspace = true
alloy-rlp.workspace = true
alloy-rpc-types-debug.workspace = true
alloy-consensus.workspace = true

# async
futures.workspace = true

# misc
eyre.workspace = true
jsonrpsee.workspace = true
pretty_assertions.workspace = true
serde.workspace = true
serde_json.workspace = true
