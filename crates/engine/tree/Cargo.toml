[package]
name = "reth-engine-tree"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-chain-state.workspace = true
reth-chainspec = { workspace = true, optional = true }
reth-consensus.workspace = true
reth-db.workspace = true
reth-engine-primitives.workspace = true
reth-errors.workspace = true
reth-evm = { workspace = true, features = ["metrics"] }
reth-network-p2p.workspace = true
reth-payload-builder.workspace = true
reth-payload-primitives.workspace = true
reth-primitives-traits.workspace = true
reth-ethereum-primitives.workspace = true
reth-provider.workspace = true
reth-prune.workspace = true
reth-revm.workspace = true
reth-stages-api.workspace = true
reth-tasks.workspace = true
reth-trie-db.workspace = true
reth-trie-parallel.workspace = true
reth-trie-sparse = { workspace = true, features = ["std", "metrics"] }
reth-trie-sparse-parallel = { workspace = true, features = ["std"] }
reth-trie.workspace = true

# alloy
alloy-evm.workspace = true
alloy-consensus.workspace = true
alloy-eips.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true
alloy-rpc-types-engine.workspace = true

revm.workspace = true
revm-primitives.workspace = true

# common
futures.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["rt", "rt-multi-thread", "sync", "macros"] }
mini-moka = { workspace = true, features = ["sync"] }

# metrics
metrics.workspace = true
reth-metrics = { workspace = true, features = ["common"] }

# misc
schnellru.workspace = true
rayon.workspace = true
tracing.workspace = true
derive_more.workspace = true
parking_lot.workspace = true

# optional deps for test-utils
reth-prune-types = { workspace = true, optional = true }
reth-stages = { workspace = true, optional = true }
reth-static-file = { workspace = true, optional = true }
reth-tracing = { workspace = true, optional = true }

[dev-dependencies]
# reth
reth-evm-ethereum = { workspace = true, features = ["test-utils"] }
reth-chain-state = { workspace = true, features = ["test-utils"] }
reth-chainspec.workspace = true
reth-db-common.workspace = true
reth-ethereum-consensus.workspace = true
reth-ethereum-engine-primitives.workspace = true
reth-evm = { workspace = true, features = ["test-utils"] }
reth-exex-types.workspace = true
reth-network-p2p = { workspace = true, features = ["test-utils"] }
reth-prune-types.workspace = true
reth-rpc-convert.workspace = true
reth-stages = { workspace = true, features = ["test-utils"] }
reth-static-file.workspace = true
reth-testing-utils.workspace = true
reth-tracing.workspace = true
reth-node-ethereum.workspace = true
reth-e2e-test-utils.workspace = true

# alloy
revm-state.workspace = true

assert_matches.workspace = true
criterion.workspace = true
eyre.workspace = true
serde_json.workspace = true
crossbeam-channel.workspace = true
proptest.workspace = true
rand.workspace = true
rand_08.workspace = true

[[bench]]
name = "channel_perf"
harness = false

[[bench]]
name = "state_root_task"
harness = false

[features]
test-utils = [
    "reth-chain-state/test-utils",
    "reth-chainspec/test-utils",
    "reth-consensus/test-utils",
    "reth-db/test-utils",
    "reth-evm/test-utils",
    "reth-network-p2p/test-utils",
    "reth-payload-builder/test-utils",
    "reth-primitives-traits/test-utils",
    "reth-provider/test-utils",
    "reth-prune-types",
    "reth-prune-types?/test-utils",
    "reth-revm/test-utils",
    "reth-stages-api/test-utils",
    "reth-stages/test-utils",
    "reth-static-file",
    "reth-tracing",
    "reth-trie/test-utils",
    "reth-trie-sparse/test-utils",
    "reth-prune-types?/test-utils",
    "reth-trie-db/test-utils",
    "reth-trie-parallel/test-utils",
    "reth-ethereum-primitives/test-utils",
    "reth-node-ethereum/test-utils",
    "reth-evm-ethereum/test-utils",
]

[[test]]
name = "e2e_testsuite"
path = "tests/e2e-testsuite/main.rs"
