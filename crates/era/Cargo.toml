[package]
name = "reth-era"
description = "e2store and era1 files core logic"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
thiserror.workspace = true

# alloy
alloy-consensus.workspace = true
alloy-eips.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true

reth-ethereum-primitives.workspace = true

# compression and decompression
snap.workspace = true

# ssz encoding and decoding
ethereum_ssz.workspace = true
ethereum_ssz_derive.workspace = true

[dev-dependencies]
eyre.workspace = true
rand.workspace = true
reqwest.workspace = true
reth-era-downloader.workspace = true
tempfile.workspace = true
tokio = { workspace = true, features = ["sync", "macros", "time", "rt-multi-thread"] }
test-case.workspace = true

[lints]
workspace = true
