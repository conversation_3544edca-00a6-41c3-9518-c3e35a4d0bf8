[package]
name = "reth-exex-test-utils"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
## reth
reth-chainspec.workspace = true
reth-config.workspace = true
reth-consensus = { workspace = true, features = ["test-utils"] }
reth-db = { workspace = true, features = ["test-utils"] }
reth-db-common.workspace = true
reth-evm-ethereum = { workspace = true, features = ["test-utils"] }
reth-execution-types.workspace = true
reth-exex.workspace = true
reth-payload-builder.workspace = true
reth-network.workspace = true
reth-node-api.workspace = true
reth-node-core.workspace = true
reth-node-builder = { workspace = true, features = ["test-utils"] }
reth-node-ethereum.workspace = true
reth-primitives-traits.workspace = true
reth-ethereum-primitives.workspace = true
reth-provider = { workspace = true, features = ["test-utils"] }
reth-tasks.workspace = true
reth-transaction-pool = { workspace = true, features = ["test-utils"] }
reth-trie-db.workspace = true

## alloy
alloy-eips.workspace = true

## async
futures-util.workspace = true
tokio.workspace = true

## misc
eyre.workspace = true
tempfile.workspace = true
thiserror.workspace = true
