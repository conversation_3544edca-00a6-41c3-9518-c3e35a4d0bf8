[package]
name = "reth-exex-types"
version.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
description = "Commonly used types for exex usage in reth."

[lints]
workspace = true

[dependencies]
# reth
reth-chain-state.workspace = true
reth-execution-types.workspace = true
reth-primitives-traits.workspace = true

# reth
alloy-primitives.workspace = true
alloy-eips.workspace = true

# misc
serde = { workspace = true, optional = true }
serde_with = { workspace = true, optional = true }

[dev-dependencies]
reth-primitives-traits = { workspace = true, features = ["arbitrary"] }
reth-ethereum-primitives = { workspace = true, features = ["arbitrary", "std"] }
arbitrary.workspace = true
bincode.workspace = true
rand.workspace = true

[features]
default = []
serde = [
    "dep:serde",
    "reth-execution-types/serde",
    "alloy-eips/serde",
    "alloy-primitives/serde",
    "rand/serde",
    "reth-primitives-traits/serde",
    "reth-ethereum-primitives/serde",
    "reth-chain-state/serde",
]
serde-bincode-compat = [
    "reth-execution-types/serde-bincode-compat",
    "serde_with",
    "alloy-eips/serde-bincode-compat",
    "reth-primitives-traits/serde-bincode-compat",
    "reth-ethereum-primitives/serde-bincode-compat",
]
