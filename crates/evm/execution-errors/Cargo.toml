[package]
name = "reth-execution-errors"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-storage-errors.workspace = true

alloy-evm.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true
nybbles.workspace = true

thiserror.workspace = true

[features]
default = ["std"]
std = [
    "alloy-primitives/std",
    "alloy-rlp/std",
    "thiserror/std",
    "nybbles/std",
    "reth-storage-errors/std",
    "alloy-evm/std",
]
