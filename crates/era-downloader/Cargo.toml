[package]
name = "reth-era-downloader"
description = "An asynchronous stream interface for downloading ERA1 files"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
exclude.workspace = true

[dependencies]
# alloy
alloy-primitives.workspace = true

# reth
reth-fs-util.workspace = true

# http
bytes.workspace = true
reqwest.workspace = true
reqwest.default-features = false
reqwest.features = ["stream", "rustls-tls-native-roots"]

# async
tokio.workspace = true
tokio.features = ["fs", "io-util", "macros"]
futures-util.workspace = true

# errors
eyre.workspace = true

# crypto
sha2.workspace = true
sha2.features = ["std"]

[dev-dependencies]
tempfile.workspace = true
test-case.workspace = true
futures.workspace = true

[lints]
workspace = true
