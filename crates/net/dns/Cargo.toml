[package]
name = "reth-dns-discovery"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Support for EIP-1459 Node Discovery via DNS"

[lints]
workspace = true

[dependencies]
# reth
reth-ethereum-forks.workspace = true
reth-network-peers = { workspace = true, features = ["secp256k1"] }
reth-tokio-util = { workspace = true, features = ["time"] }

# ethereum
alloy-primitives.workspace = true
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery", "serde"] }
enr.workspace = true

# async/futures
tokio = { workspace = true, features = ["io-util", "net", "time"] }
tokio-stream.workspace = true

# trust-dns
hickory-resolver = { workspace = true, features = ["tokio"] }

# misc
data-encoding.workspace = true
linked_hash_set.workspace = true
schnellru.workspace = true
thiserror.workspace = true
tracing.workspace = true
parking_lot.workspace = true
serde = { workspace = true, optional = true }
serde_with = { workspace = true, optional = true }

[dev-dependencies]
reth-chainspec.workspace = true
alloy-rlp.workspace = true
alloy-chains.workspace = true
tokio = { workspace = true, features = ["sync", "rt", "rt-multi-thread"] }
reth-tracing.workspace = true
rand.workspace = true

[features]
serde = [
    "dep:serde",
    "dep:serde_with",
    "alloy-chains/serde",
    "alloy-primitives/serde",
    "enr/serde",
    "linked_hash_set/serde",
    "parking_lot/serde",
    "rand/serde",
    "secp256k1/serde",
    "hickory-resolver/serde",
    "reth-ethereum-forks/serde",
]
