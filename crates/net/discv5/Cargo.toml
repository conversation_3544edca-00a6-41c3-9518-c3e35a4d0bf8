[package]
name = "reth-discv5"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Ethereum peer discovery V5"

[lints]
workspace = true

[dependencies]
# reth
reth-chainspec.workspace = true
reth-ethereum-forks.workspace = true
reth-metrics.workspace = true
reth-network-peers = { workspace = true, features = ["secp256k1"] }

# ethereum
alloy-primitives.workspace = true
alloy-rlp.workspace = true
discv5 = { workspace = true, features = ["libp2p"] }
enr.workspace = true
secp256k1.workspace = true

# async/futures
tokio.workspace = true
futures.workspace = true

# io
rand.workspace = true

# misc
derive_more.workspace = true
tracing.workspace = true
thiserror.workspace = true
itertools.workspace = true
metrics.workspace = true

[dev-dependencies]
reth-tracing.workspace = true
tokio = { workspace = true, features = ["rt-multi-thread"] }
secp256k1 = { workspace = true, features = ["std", "rand"] }
rand_08.workspace = true
