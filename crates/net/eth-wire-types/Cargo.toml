[package]
name = "reth-eth-wire-types"
description = "types for eth-wire"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-chainspec.workspace = true
reth-codecs-derive.workspace = true
reth-ethereum-primitives.workspace = true
reth-primitives-traits.workspace = true

# ethereum
alloy-chains = { workspace = true, features = ["rlp"] }
alloy-eips.workspace = true
alloy-primitives = { workspace = true, features = ["map"] }
alloy-rlp = { workspace = true, features = ["derive"] }
alloy-consensus.workspace = true

bytes.workspace = true
derive_more.workspace = true
serde = { workspace = true, optional = true }
thiserror.workspace = true

# arbitrary utils
arbitrary = { workspace = true, features = ["derive"], optional = true }
proptest = { workspace = true, optional = true }
proptest-arbitrary-interop = { workspace = true, optional = true }
alloy-hardforks.workspace = true

[dev-dependencies]
reth-ethereum-primitives = { workspace = true, features = ["arbitrary"] }
alloy-primitives = { workspace = true, features = ["arbitrary", "rand"] }
alloy-consensus = { workspace = true, features = ["arbitrary"] }
alloy-eips = { workspace = true, features = ["arbitrary"] }
alloy-genesis.workspace = true
alloy-chains = { workspace = true, features = ["arbitrary"] }
arbitrary = { workspace = true, features = ["derive"] }
proptest.workspace = true
proptest-arbitrary-interop.workspace = true
rand.workspace = true

[features]
default = ["std"]
std = [
    "alloy-chains/std",
    "alloy-consensus/std",
    "alloy-eips/std",
    "alloy-genesis/std",
    "alloy-primitives/std",
    "alloy-rlp/std",
    "bytes/std",
    "derive_more/std",
    "reth-ethereum-primitives/std",
    "reth-primitives-traits/std",
    "serde?/std",
    "thiserror/std",
    "reth-chainspec/std",
]
arbitrary = [
    "reth-ethereum-primitives/arbitrary",
    "alloy-chains/arbitrary",
    "dep:arbitrary",
    "dep:proptest",
    "dep:proptest-arbitrary-interop",
    "reth-chainspec/arbitrary",
    "alloy-consensus/arbitrary",
    "alloy-eips/arbitrary",
    "alloy-primitives/arbitrary",
    "reth-primitives-traits/arbitrary",
]
serde = [
    "dep:serde",
    "alloy-chains/serde",
    "alloy-consensus/serde",
    "alloy-eips/serde",
    "alloy-primitives/serde",
    "bytes/serde",
    "rand/serde",
    "reth-primitives-traits/serde",
    "reth-ethereum-primitives/serde",
    "alloy-hardforks/serde",
]
