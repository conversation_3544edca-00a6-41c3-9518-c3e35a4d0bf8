[package]
name = "reth-network-api"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Network interfaces and commonly used types"

[lints]
workspace = true

[dependencies]
# reth
reth-network-peers.workspace = true
reth-network-types.workspace = true
reth-network-p2p.workspace = true
reth-eth-wire-types.workspace = true
reth-tokio-util.workspace = true
reth-ethereum-forks.workspace = true

# ethereum
alloy-consensus.workspace = true
alloy-rpc-types-eth.workspace = true
alloy-primitives = { workspace = true, features = ["getrandom"] }
alloy-rpc-types-admin.workspace = true
enr = { workspace = true, default-features = false, features = ["rust-secp256k1"] }

# async
futures.workspace = true
tokio-stream.workspace = true

# misc
thiserror.workspace = true
serde = { workspace = true, features = ["derive"], optional = true }
tokio = { workspace = true, features = ["sync"] }
auto_impl.workspace = true
derive_more.workspace = true

[features]
serde = [
    "dep:serde",
    "reth-eth-wire-types/serde",
    "reth-network-types/serde",
    "alloy-primitives/serde",
    "enr/serde",
    "reth-ethereum-forks/serde",
    "alloy-consensus/serde",
    "alloy-rpc-types-eth/serde",
]
