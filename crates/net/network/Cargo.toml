[package]
name = "reth-network"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Ethereum network support"

[lints]
workspace = true

[dependencies]
# reth
reth-chainspec.workspace = true
reth-fs-util.workspace = true
reth-primitives-traits.workspace = true
reth-net-banlist.workspace = true
reth-network-api.workspace = true
reth-network-p2p.workspace = true
reth-discv4.workspace = true
reth-discv5.workspace = true
reth-dns-discovery.workspace = true
reth-ethereum-forks.workspace = true
reth-eth-wire.workspace = true
reth-eth-wire-types.workspace = true
reth-ecies.workspace = true
reth-tasks.workspace = true
reth-transaction-pool.workspace = true
reth-storage-api.workspace = true
reth-tokio-util.workspace = true
reth-consensus.workspace = true
reth-network-peers = { workspace = true, features = ["net"] }
reth-network-types.workspace = true

# ethereum
alloy-consensus.workspace = true
alloy-eips.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true
enr = { workspace = true, features = ["serde", "rust-secp256k1"] }
discv5.workspace = true

# reth-ethereum
reth-ethereum-primitives.workspace = true

# async/futures
futures.workspace = true
pin-project.workspace = true
tokio = { workspace = true, features = ["io-util", "net", "macros", "rt-multi-thread", "time"] }
tokio-stream.workspace = true
tokio-util = { workspace = true, features = ["codec"] }

# io
serde = { workspace = true, optional = true }

# metrics
reth-metrics = { workspace = true, features = ["common"] }
metrics.workspace = true

# misc
auto_impl.workspace = true
aquamarine.workspace = true
tracing.workspace = true
rustc-hash.workspace = true
thiserror.workspace = true
parking_lot.workspace = true
rand.workspace = true
rand_08.workspace = true
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery"] }
derive_more.workspace = true
schnellru.workspace = true
itertools.workspace = true
smallvec.workspace = true

[dev-dependencies]
# reth
reth-discv4 = { workspace = true, features = ["test-utils"] }

# we need to enable the test-utils feature in our own crate to use utils in
# integration tests
reth-network = { workspace = true, features = ["test-utils"] }
reth-network-p2p = { workspace = true, features = ["test-utils"] }
reth-network-types = { workspace = true, features = ["test-utils"] }

reth-provider = { workspace = true, features = ["test-utils"] }
reth-tracing.workspace = true
reth-transaction-pool = { workspace = true, features = ["test-utils"] }

# alloy deps for testing against nodes
alloy-genesis.workspace = true

# misc
tempfile.workspace = true
url.workspace = true
secp256k1 = { workspace = true, features = ["rand"] }

## Benchmarks
criterion = { workspace = true, features = ["async_tokio", "html_reports"] }

[features]
serde = [
    "dep:serde",
    "secp256k1/serde",
    "enr/serde",
    "reth-network-types/serde",
    "reth-dns-discovery/serde",
    "reth-eth-wire/serde",
    "reth-eth-wire-types/serde",
    "alloy-consensus/serde",
    "alloy-eips/serde",
    "alloy-primitives/serde",
    "discv5/serde",
    "parking_lot/serde",
    "rand/serde",
    "smallvec/serde",
    "url/serde",
    "reth-primitives-traits/serde",
    "reth-ethereum-forks/serde",
    "reth-network/serde",
    "reth-transaction-pool/serde",
    "reth-ethereum-primitives/serde",
    "reth-network-api/serde",
    "rand_08/serde",
    "reth-storage-api/serde",
]
test-utils = [
    "reth-transaction-pool/test-utils",
    "reth-network-types/test-utils",
    "reth-chainspec/test-utils",
    "reth-consensus/test-utils",
    "reth-discv4/test-utils",
    "reth-network/test-utils",
    "reth-network-p2p/test-utils",
    "reth-primitives-traits/test-utils",
    "reth-provider/test-utils",
    "reth-ethereum-primitives/test-utils",
]

[[bench]]
name = "broadcast"
required-features = ["test-utils"]
harness = false

[[bench]]
name = "tx_manager_hash_fetching"
required-features = ["test-utils"]
harness = false
