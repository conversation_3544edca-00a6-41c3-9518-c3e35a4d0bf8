graph TB
  handle(NetworkHandle)
  events(NetworkEvents)
  transactions(Transactions Task)
  ethrequest(ETH Request Task)
  discovery(Discovery Task)
  subgraph NetworkManager
    direction LR
    subgraph Swarm
        direction TB
        B1[(Session Manager)]
        B2[(Connection Listener)]
        B3[(Network State)]
    end
 end
 handle <--> |request response channel| NetworkManager
 NetworkManager --> |Network events| events
 transactions <--> |transactions| NetworkManager
 ethrequest <--> |ETH request handing| NetworkManager
 discovery --> |Discovered peers| NetworkManager
