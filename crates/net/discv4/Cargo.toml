[package]
name = "reth-discv4"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Ethereum network discovery"

[lints]
workspace = true

[dependencies]
# reth
reth-net-banlist.workspace = true
reth-ethereum-forks.workspace = true
reth-net-nat.workspace = true
reth-network-peers = { workspace = true, features = ["secp256k1"] }

# ethereum
alloy-primitives = { workspace = true, features = ["rand"] }
alloy-rlp = { workspace = true, features = ["derive"] }
discv5.workspace = true
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery", "serde"] }
enr.workspace = true

# async/futures
tokio = { workspace = true, features = ["io-util", "net", "time"] }
tokio-stream.workspace = true

# misc
schnellru.workspace = true
tracing.workspace = true
thiserror.workspace = true
parking_lot.workspace = true
rand_08 = { workspace = true, optional = true }
generic-array.workspace = true
serde = { workspace = true, optional = true }
itertools.workspace = true

[dev-dependencies]
secp256k1 = { workspace = true, features = ["rand"] }
assert_matches.workspace = true
rand_08.workspace = true
tokio = { workspace = true, features = ["macros", "rt-multi-thread"] }
reth-tracing.workspace = true

[features]
default = ["serde"]
serde = [
    "dep:serde",
    "alloy-primitives/serde",
    "discv5/serde",
    "enr/serde",
    "generic-array/serde",
    "parking_lot/serde",
    "rand_08?/serde",
    "secp256k1/serde",
    "reth-ethereum-forks/serde",
]
test-utils = ["dep:rand_08"]
