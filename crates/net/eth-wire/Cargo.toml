[package]
name = "reth-eth-wire"
description = "Implements the eth/64 and eth/65 P2P protocols"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true

[lints]
workspace = true

[dependencies]
# reth
reth-codecs.workspace = true
reth-primitives-traits.workspace = true
reth-ecies.workspace = true
alloy-rlp = { workspace = true, features = ["derive"] }
reth-eth-wire-types.workspace = true
reth-network-peers.workspace = true
reth-ethereum-forks.workspace = true

# ethereum
alloy-primitives.workspace = true
alloy-chains.workspace = true

# metrics
reth-metrics.workspace = true

bytes.workspace = true
derive_more.workspace = true
thiserror.workspace = true
serde = { workspace = true, optional = true }
tokio = { workspace = true, features = ["macros", "net", "sync", "time"] }
tokio-util = { workspace = true, features = ["io", "codec"] }
futures.workspace = true
tokio-stream.workspace = true
pin-project.workspace = true
tracing.workspace = true
snap.workspace = true

# arbitrary utils
arbitrary = { workspace = true, features = ["derive"], optional = true }

[dev-dependencies]
reth-primitives-traits = { workspace = true, features = ["arbitrary"] }
reth-eth-wire-types = { workspace = true, features = ["arbitrary"] }
reth-tracing.workspace = true

alloy-consensus.workspace = true
test-fuzz.workspace = true
tokio = { workspace = true, features = ["rt", "rt-multi-thread"] }
rand.workspace = true
secp256k1 = { workspace = true, features = ["global-context", "std", "recovery"] }
rand_08.workspace = true

arbitrary = { workspace = true, features = ["derive"] }
proptest.workspace = true
proptest-arbitrary-interop.workspace = true
async-stream.workspace = true
serde.workspace = true
alloy-eips.workspace = true

[features]
arbitrary = [
    "reth-eth-wire-types/arbitrary",
    "dep:arbitrary",
    "alloy-eips/arbitrary",
    "alloy-primitives/arbitrary",
    "reth-codecs/arbitrary",
    "alloy-chains/arbitrary",
    "reth-primitives-traits/arbitrary",
    "reth-ethereum-forks/arbitrary",
    "alloy-consensus/arbitrary",
]
serde = [
    "dep:serde",
    "reth-eth-wire-types/serde",
    "alloy-eips/serde",
    "alloy-primitives/serde",
    "bytes/serde",
    "rand/serde",
    "secp256k1/serde",
    "reth-codecs/serde",
    "alloy-chains/serde",
    "reth-primitives-traits/serde",
    "reth-ethereum-forks/serde",
    "alloy-consensus/serde",
    "rand_08/serde",
]

[[test]]
name = "fuzz_roundtrip"
path = "tests/fuzz_roundtrip.rs"
required-features = ["arbitrary", "serde"]
