[package]
name = "reth-downloaders"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
description = "Implementations of various block downloaders"

[lints]
workspace = true

[dependencies]
# reth
reth-config.workspace = true
reth-consensus.workspace = true
reth-network-p2p.workspace = true
reth-network-peers.workspace = true
reth-primitives-traits.workspace = true
reth-storage-api.workspace = true
reth-tasks.workspace = true

# optional deps for the test-utils feature
reth-db = { workspace = true, optional = true }
reth-db-api = { workspace = true, optional = true }
reth-ethereum-primitives = { workspace = true, optional = true }
reth-testing-utils = { workspace = true, optional = true }

# ethereum
alloy-consensus.workspace = true
alloy-eips.workspace = true
alloy-primitives.workspace = true
alloy-rlp.workspace = true

# async
futures.workspace = true
futures-util.workspace = true
pin-project.workspace = true
tokio = { workspace = true, features = ["sync", "fs", "io-util"] }
tokio-stream.workspace = true
tokio-util = { workspace = true, features = ["codec"] }

# metrics
reth-metrics.workspace = true
metrics.workspace = true

# misc
rayon.workspace = true
thiserror.workspace = true
tracing.workspace = true

tempfile = { workspace = true, optional = true }
itertools.workspace = true

[dev-dependencies]
reth-ethereum-primitives.workspace = true
reth-chainspec.workspace = true
reth-db = { workspace = true, features = ["test-utils"] }
reth-db-api.workspace = true
reth-consensus = { workspace = true, features = ["test-utils"] }
reth-network-p2p = { workspace = true, features = ["test-utils"] }
reth-provider = { workspace = true, features = ["test-utils"] }
reth-testing-utils.workspace = true
reth-tracing.workspace = true

assert_matches.workspace = true
tokio = { workspace = true, features = ["macros", "rt-multi-thread"] }
rand.workspace = true
tempfile.workspace = true

[features]
test-utils = [
    "tempfile",
    "reth-db-api",
    "reth-db/test-utils",
    "reth-consensus/test-utils",
    "reth-network-p2p/test-utils",
    "reth-testing-utils",
    "reth-chainspec/test-utils",
    "reth-db-api?/test-utils",
    "reth-provider/test-utils",
    "reth-primitives-traits/test-utils",
    "dep:reth-ethereum-primitives",
    "reth-ethereum-primitives?/test-utils",
]
