[package]
name = "reth-primitives"
version.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
repository.workspace = true
rust-version.workspace = true
description = "Commonly used types in reth."

[lints]
workspace = true

[dependencies]
# reth
reth-ethereum-primitives = { workspace = true, features = ["serde"] }
reth-primitives-traits = { workspace = true, features = ["serde"] }
reth-ethereum-forks.workspace = true
reth-static-file-types.workspace = true

# ethereum
alloy-consensus.workspace = true

# for eip-4844
c-kzg = { workspace = true, features = ["serde"], optional = true }

# misc
once_cell.workspace = true

# arbitrary utils
arbitrary = { workspace = true, features = ["derive"], optional = true }

[dev-dependencies]
# eth
reth-primitives-traits = { workspace = true, features = ["arbitrary", "test-utils"] }

alloy-primitives.workspace = true
alloy-rlp.workspace = true
alloy-eips = { workspace = true, features = ["arbitrary"] }
alloy-genesis.workspace = true

arbitrary = { workspace = true, features = ["derive"] }

proptest-arbitrary-interop.workspace = true
proptest.workspace = true
reth-codecs.workspace = true

criterion.workspace = true

[features]
default = ["c-kzg", "alloy-compat", "std", "reth-codec", "secp256k1"]
std = [
    "reth-primitives-traits/std",
    "alloy-consensus/std",
    "alloy-eips/std",
    "alloy-genesis/std",
    "once_cell/std",
    "reth-ethereum-forks/std",
    "reth-ethereum-primitives/std",
    "alloy-rlp/std",
    "alloy-primitives/std",
    "reth-static-file-types/std",
]
reth-codec = [
    "std",
    "reth-primitives-traits/reth-codec",
    "reth-ethereum-primitives/reth-codec",
]
asm-keccak = [
    "alloy-primitives/asm-keccak",
]
arbitrary = [
    "dep:arbitrary",
    "alloy-eips/arbitrary",
    "reth-codec",
    "reth-ethereum-forks/arbitrary",
    "reth-primitives-traits/arbitrary",
    "alloy-consensus/arbitrary",
    "reth-ethereum-primitives/arbitrary",
    "reth-codecs/arbitrary",
    "alloy-primitives/arbitrary",
    "c-kzg?/arbitrary",
]
secp256k1 = [
    "reth-primitives-traits/secp256k1",
]
c-kzg = [
    "dep:c-kzg",
    "alloy-consensus/kzg",
    "alloy-eips/kzg",
]
alloy-compat = []
test-utils = [
    "reth-primitives-traits/test-utils",
    "arbitrary",
    "reth-codecs/test-utils",
    "reth-ethereum-primitives/test-utils",
]
serde-bincode-compat = [
    "alloy-eips/serde-bincode-compat",
    "alloy-consensus/serde-bincode-compat",
    "reth-primitives-traits/serde-bincode-compat",
    "reth-ethereum-primitives/serde-bincode-compat",
    "alloy-genesis/serde-bincode-compat",
]

[[bench]]
name = "recover_ecdsa_crit"
harness = false

[[bench]]
name = "validate_blob_tx"
required-features = ["arbitrary", "c-kzg"]
harness = false
